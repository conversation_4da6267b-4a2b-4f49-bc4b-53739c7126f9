apiVersion: apps/v1
kind: Deployment
metadata:
  name: placement-frontend
  labels:
    app: placement-frontend
    environment: production
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: placement-frontend
  template:
    metadata:
      labels:
        app: placement-frontend
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
    spec:
      serviceAccountName: placement-frontend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 101
        fsGroup: 101
      containers:
      - name: frontend
        image: ************.dkr.ecr.us-east-1.amazonaws.com/placement-portal-frontend:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: REACT_APP_API_URL
          value: "http://placement-backend-service:8080"
        # Temporarily removing resource requests to allow scheduling on constrained nodes
        # resources:
        #   requests:
        #     memory: "16Mi"
        #     cpu: "10m"
        #   limits:
        #     memory: "32Mi"
        #     cpu: "25m"
        # securityContext:
        #   allowPrivilegeEscalation: false
        #   readOnlyRootFilesystem: true
        #   capabilities:
        #     drop:
        #     - ALL
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        # volumeMounts:
        # - name: nginx-cache
        #   mountPath: /var/cache/nginx
        # - name: nginx-run
        #   mountPath: /var/run
      # volumes:
      # - name: nginx-cache
      #   emptyDir: {}
      # - name: nginx-run
      #   emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - placement-frontend
              topologyKey: kubernetes.io/hostname
