apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  NODE_ENV: "production"
  PORT: "5000"
  DB_NAME: "placement_db"
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: placement-portal-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "placement-portal-ip"
    networking.gke.io/managed-certificates: "placement-portal-ssl"
    kubernetes.io/ingress.class: "gce"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - placement-portal.com
    - www.placement-portal.com
    - api.placement-portal.com
    secretName: placement-portal-tls
  rules:
  - host: placement-portal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: placement-frontend-service
            port:
              number: 80
  - host: www.placement-portal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: placement-frontend-service
            port:
              number: 80
  - host: api.placement-portal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: placement-backend-service
            port:
              number: 5000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: placement-backend-service
            port:
              number: 5000
      - path: /health
        pathType: Exact
        backend:
          service:
            name: placement-backend-service
            port:
              number: 5000
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: placement-portal-ssl
spec:
  domains:
    - placement-portal.com
    - www.placement-portal.com
    - api.placement-portal.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: placement-portal-redirect
  annotations:
    kubernetes.io/ingress.class: "gce"
    nginx.ingress.kubernetes.io/permanent-redirect: "https://placement-portal.com$request_uri"
spec:
  rules:
  - host: www.placement-portal.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: placement-frontend-service
            port:
              number: 80