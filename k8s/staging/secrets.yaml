apiVersion: v1
kind: Secret
metadata:
  name: database-secret
  namespace: staging
type: Opaque
data:
  mongodb-uri: bW9uZ29kYjovL3VzZXI6cGFzcw== # base64 encoded mongodb connection string
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
  namespace: staging
type: Opaque
data:
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ== # base64 encoded JWT secret
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: staging
data:
  NODE_ENV: "staging"
  LOG_LEVEL: "debug"
  API_VERSION: "v1"