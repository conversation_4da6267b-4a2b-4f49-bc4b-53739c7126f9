apiVersion: apps/v1
kind: Deployment
metadata:
  name: placement-backend
  namespace: staging
  labels:
    app: placement-backend
    environment: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: placement-backend
  template:
    metadata:
      labels:
        app: placement-backend
        environment: staging
    spec:
      containers:
      - name: backend
        image: 011528267161.dkr.ecr.us-east-1.amazonaws.com/placement-portal-backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          value: "staging"
        - name: PORT
          value: "5000"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: mongodb-uri
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: placement-backend-service
  namespace: staging
  labels:
    app: placement-backend
    environment: staging
spec:
  selector:
    app: placement-backend
  ports:
  - port: 5000
    targetPort: 5000
    protocol: TCP
  type: LoadBalancer