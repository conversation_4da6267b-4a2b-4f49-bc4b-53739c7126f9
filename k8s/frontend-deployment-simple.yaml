apiVersion: apps/v1
kind: Deployment
metadata:
  name: placement-frontend
  labels:
    app: placement-frontend
    environment: production
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: placement-frontend
  template:
    metadata:
      labels:
        app: placement-frontend
        environment: production
    spec:
      containers:
      - name: frontend
        image: gcr.io/avid-sunset-435316-a6/placement-frontend:v2
        ports:
        - containerPort: 80
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: REACT_APP_API_URL
          value: "http://placement-backend-service:5000"
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
