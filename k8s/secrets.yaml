apiVersion: v1
kind: Secret
metadata:
  name: db-secret
type: Opaque
data:
  # Base64 encoded database connection details
  host: MTAuNzcuMC40  # ********* (your Cloud SQL private IP)
  username: cGxhY2VtZW50  # placement (base64 encoded)
  password: UGxhY2VtZW50QDEyMw==  # Placement@123 (base64 encoded)
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
type: Opaque
data:
  jwt-secret: eW91ci1zdXBlci1zZWN1cmUtand0LXNlY3JldC1rZXktaGVyZQ== # your-super-secure-jwt-secret-key-here
---
# Service Accounts
apiVersion: v1
kind: ServiceAccount
metadata:
  name: placement-backend-sa
  labels:
    app: placement-backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: placement-frontend-sa
  labels:
    app: placement-frontend
---
# Enhanced Secrets with DocumentDB MongoDB connection
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
type: Opaque
data:
  # DocumentDB MongoDB connection string - Replace with actual DocumentDB endpoint
  mongodb-uri: ************************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: cache-secret
type: Opaque
data:
  # For now, using in-memory cache (can be upgraded to Redis later)
  redis-url: aW4tbWVtb3J5  # in-memory
---
# Network Policies for Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: placement-backend-netpol
spec:
  podSelector:
    matchLabels:
      app: placement-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: placement-frontend
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 5000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 27017  # MongoDB/DocumentDB
    - protocol: TCP
      port: 443    # HTTPS
    - protocol: TCP
      port: 53     # DNS
    - protocol: UDP
      port: 53     # DNS
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: placement-frontend-netpol
spec:
  podSelector:
    matchLabels:
      app: placement-frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: placement-backend
    ports:
    - protocol: TCP
      port: 5000