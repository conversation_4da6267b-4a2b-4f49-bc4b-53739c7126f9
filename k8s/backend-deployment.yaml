apiVersion: apps/v1
kind: Deployment
metadata:
  name: placement-backend
  labels:
    app: placement-backend
    environment: production
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: placement-backend
  template:
    metadata:
      labels:
        app: placement-backend
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: placement-backend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: ************.dkr.ecr.us-east-1.amazonaws.com/placement-portal-backend:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-uri
        - name: JWT_SECRET
          value: "placement_secret_key_change_in_production"
        # Temporarily removing resource requests to allow scheduling on constrained nodes
        # resources:
        #   requests:
        #     memory: "32Mi"
        #     cpu: "25m"
        #   limits:
        #     memory: "64Mi"
        #     cpu: "50m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        # Temporarily removing health checks to get the app running
        # livenessProbe:
        #   httpGet:
        #     path: /health
        #     port: 5000
        #   initialDelaySeconds: 60
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /ready
        #     port: 5000
        #   initialDelaySeconds: 10
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
        # startupProbe:
        #   httpGet:
        #     path: /health
        #     port: 5000
        #   initialDelaySeconds: 10
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 30
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - placement-backend
              topologyKey: kubernetes.io/hostname
