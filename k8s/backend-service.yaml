apiVersion: v1
kind: Service
metadata:
  name: placement-backend-service
  labels:
    app: placement-backend
    environment: production
  annotations:
    service.beta.kubernetes.io/gcp-load-balancer-type: "Internal"
spec:
  selector:
    app: placement-backend
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: placement-frontend-service
  labels:
    app: placement-frontend
    environment: production
  annotations:
    service.beta.kubernetes.io/gcp-load-balancer-type: "Internal"
spec:
  selector:
    app: placement-frontend
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  type: ClusterIP
