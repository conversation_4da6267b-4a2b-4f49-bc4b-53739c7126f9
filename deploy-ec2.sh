#!/bin/bash

# EC2 Deployment Script for Placement Portal
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
IMAGE_TAG=${IMAGE_TAG:-latest}

echo -e "${GREEN}🚀 Starting EC2 Deployment for Placement Portal${NC}"
echo "========================================================"

# Function to check if AWS CLI is configured
check_aws_config() {
    print_status "Checking AWS configuration..."
    
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS CLI not configured. Please run 'aws configure'"
        exit 1
    fi
    
    AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    print_success "AWS Account ID: $AWS_ACCOUNT_ID"
}

# Function to get ECR login
ecr_login() {
    print_status "Logging into ECR..."
    
    ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
    
    print_success "ECR login successful"
}

# Function to build and push images
build_and_push_images() {
    print_status "Building and pushing Docker images..."
    
    # Build backend image
    print_status "Building backend image..."
    cd backend
    docker build -t $ECR_REGISTRY/placement-portal-backend:$IMAGE_TAG .
    docker push $ECR_REGISTRY/placement-portal-backend:$IMAGE_TAG
    cd ..
    
    # Build frontend image
    print_status "Building frontend image..."
    cd frontend
    docker build -t $ECR_REGISTRY/placement-portal-frontend:$IMAGE_TAG .
    docker push $ECR_REGISTRY/placement-portal-frontend:$IMAGE_TAG
    cd ..
    
    print_success "Docker images built and pushed successfully"
}

# Function to update launch templates
update_launch_templates() {
    print_status "Updating launch templates with new image tags..."
    
    # Update backend launch template
    print_status "Updating backend launch template..."
    aws ec2 create-launch-template-version \
        --launch-template-name placement-portal-backend \
        --source-version '$Latest' \
        --launch-template-data "{
            \"UserData\": \"$(echo "#!/bin/bash
cd /opt/placement-backend
export ECR_REGISTRY=$ECR_REGISTRY
export IMAGE_TAG=$IMAGE_TAG
./deploy.sh" | base64 -w 0)\"
        }" \
        --region $AWS_REGION
    
    # Update frontend launch template
    print_status "Updating frontend launch template..."
    aws ec2 create-launch-template-version \
        --launch-template-name placement-portal-frontend \
        --source-version '$Latest' \
        --launch-template-data "{
            \"UserData\": \"$(echo "#!/bin/bash
cd /opt/placement-frontend
export ECR_REGISTRY=$ECR_REGISTRY
export IMAGE_TAG=$IMAGE_TAG
./deploy.sh" | base64 -w 0)\"
        }" \
        --region $AWS_REGION
    
    print_success "Launch templates updated successfully"
}

# Function to trigger rolling deployment
trigger_rolling_deployment() {
    print_status "Triggering rolling deployment..."
    
    # Start instance refresh for backend ASG
    print_status "Starting backend instance refresh..."
    aws autoscaling start-instance-refresh \
        --auto-scaling-group-name placement-portal-backend-asg \
        --preferences MinHealthyPercentage=50,InstanceWarmup=300 \
        --region $AWS_REGION
    
    # Start instance refresh for frontend ASG
    print_status "Starting frontend instance refresh..."
    aws autoscaling start-instance-refresh \
        --auto-scaling-group-name placement-portal-frontend-asg \
        --preferences MinHealthyPercentage=50,InstanceWarmup=300 \
        --region $AWS_REGION
    
    print_success "Rolling deployment initiated"
}

# Function to wait for deployment completion
wait_for_deployment() {
    print_status "Waiting for deployment to complete..."
    
    # Wait for backend deployment
    print_status "Monitoring backend deployment..."
    while true; do
        status=$(aws autoscaling describe-instance-refreshes \
            --auto-scaling-group-name placement-portal-backend-asg \
            --region $AWS_REGION \
            --query 'InstanceRefreshes[0].Status' \
            --output text)
        
        echo "Backend refresh status: $status"
        
        if [ "$status" = "Successful" ]; then
            print_success "Backend deployment completed successfully"
            break
        elif [ "$status" = "Failed" ] || [ "$status" = "Cancelled" ]; then
            print_error "Backend deployment failed with status: $status"
            exit 1
        fi
        
        sleep 30
    done
    
    # Wait for frontend deployment
    print_status "Monitoring frontend deployment..."
    while true; do
        status=$(aws autoscaling describe-instance-refreshes \
            --auto-scaling-group-name placement-portal-frontend-asg \
            --region $AWS_REGION \
            --query 'InstanceRefreshes[0].Status' \
            --output text)
        
        echo "Frontend refresh status: $status"
        
        if [ "$status" = "Successful" ]; then
            print_success "Frontend deployment completed successfully"
            break
        elif [ "$status" = "Failed" ] || [ "$status" = "Cancelled" ]; then
            print_error "Frontend deployment failed with status: $status"
            exit 1
        fi
        
        sleep 30
    done
}

# Function to show deployment status
show_deployment_status() {
    print_status "Deployment Status:"
    
    # Get load balancer DNS
    LB_DNS=$(aws elbv2 describe-load-balancers \
        --names placement-portal-alb \
        --region $AWS_REGION \
        --query 'LoadBalancers[0].DNSName' \
        --output text)
    
    # Get ASG instance counts
    BACKEND_INSTANCES=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names placement-portal-backend-asg \
        --region $AWS_REGION \
        --query 'AutoScalingGroups[0].Instances | length(@)')
    
    FRONTEND_INSTANCES=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names placement-portal-frontend-asg \
        --region $AWS_REGION \
        --query 'AutoScalingGroups[0].Instances | length(@)')
    
    echo ""
    echo "=========================================="
    print_success "Deployment completed successfully!"
    echo "=========================================="
    echo "Application URL: http://$LB_DNS"
    echo "Backend instances: $BACKEND_INSTANCES"
    echo "Frontend instances: $FRONTEND_INSTANCES"
    echo "Image tag: $IMAGE_TAG"
    echo "=========================================="
}

# Main execution
main() {
    check_aws_config
    ecr_login
    build_and_push_images
    update_launch_templates
    trigger_rolling_deployment
    wait_for_deployment
    show_deployment_status
}

# Run main function
main "$@"
